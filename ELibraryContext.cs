
using Microsoft.EntityFrameworkCore;

public class ELibraryContext : DbContext
{
    public ELibraryContext(DbContextOptions<ELibraryContext> options) : base(options) { }

    public DbSet<User> Users { get; set; }
    public DbSet<Author> Authors { get; set; }
    public DbSet<Publisher> Publishers { get; set; }
    public DbSet<Book> Books { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        // Map to the correct table names as they exist in the database
        modelBuilder.Entity<User>().ToTable("User");
        modelBuilder.Entity<Author>().ToTable("Author");
        modelBuilder.Entity<Publisher>().ToTable("Publisher");
        modelBuilder.Entity<Book>().ToTable("Book");

        // Configure relationships
        modelBuilder.Entity<Book>()
            .HasOne(b => b.Publisher)
            .WithMany(p => p.Books)
            .HasForeignKey(b => b.PubId);

        modelBuilder.Entity<Book>()
            .HasOne(b => b.Author)
            .WithMany(a => a.Books)
            .HasForeignKey(b => b.AuthorId);

        base.OnModelCreating(modelBuilder);
    }
}
