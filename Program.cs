using Microsoft.EntityFrameworkCore;

var builder = WebApplication.CreateBuilder(args);

// إضافة تكوين URL محدد
builder.WebHost.ConfigureKestrel(serverOptions =>
{
    serverOptions.ListenAnyIP(5000); // الاستماع على جميع عناوين IP المتاحة على المنفذ 5000
});

builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

builder.Services.AddDbContext<ELibraryContext>(options =>
    options.UseSqlServer("Server=ELibraryDB.mssql.somee.com,1433;Database=ELibraryDB;User Id=omarzafari98_SQLLogin_1;Password=**********;TrustServerCertificate=True"));

// Register services
builder.Services.AddScoped<AuthService>();

var app = builder.Build();

// Enable Swagger in all environments for testing
app.UseSwagger();
app.UseSwaggerUI();

app.UseAuthorization();
app.MapControllers();
app.Run();
