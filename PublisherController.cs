using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

[ApiController]
[Route("api/[controller]")]
public class PublisherController : ControllerBase
{
    private readonly ELibraryContext _context;

    public PublisherController(ELibraryContext context)
    {
        _context = context;
    }

    [HttpPost]
    [Authorize]
    public async Task<IActionResult> AddPublisher([FromBody] Publisher publisher)
    {
        // Check if user is admin
        var isAdmin = User.FindFirst("IsAdmin")?.Value;
        if (isAdmin != "True")
        {
            return Forbid("Only admins can add publishers");
        }

        _context.Publishers.Add(publisher);
        await _context.SaveChangesAsync();

        return Ok(publisher);
    }

    [HttpGet]
    public async Task<IActionResult> GetAllPublishers()
    {
        var publishers = await _context.Publishers.ToListAsync();
        return Ok(publishers);
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> GetPublisherDetails(int id)
    {
        var publisher = await _context.Publishers
            .Include(p => p.Books)
            .ThenInclude(b => b.Author)
            .FirstOrDefaultAsync(p => p.Id == id);

        if (publisher == null)
        {
            return NotFound("Publisher not found");
        }

        return Ok(publisher);
    }

    [HttpGet("search")]
    public async Task<IActionResult> SearchPublishers([FromQuery] string name)
    {
        if (string.IsNullOrEmpty(name))
        {
            return BadRequest("Name parameter is required");
        }

        var publishers = await _context.Publishers
            .Where(p => p.PName.Contains(name))
            .ToListAsync();

        return Ok(publishers);
    }

    [HttpGet("{id}/books")]
    public async Task<IActionResult> GetPublisherBooks(int id)
    {
        var publisher = await _context.Publishers
            .Include(p => p.Books)
            .ThenInclude(b => b.Author)
            .FirstOrDefaultAsync(p => p.Id == id);

        if (publisher == null)
        {
            return NotFound("Publisher not found");
        }

        return Ok(publisher.Books);
    }
}
