using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

[ApiController]
[Route("api/[controller]")]
public class PublisherController : ControllerBase
{
    private readonly ELibraryContext _context;

    public PublisherController(ELibraryContext context)
    {
        _context = context;
    }

    [HttpPost]
    public async Task<IActionResult> AddPublisher([FromBody] AddPublisherRequest request)
    {
        // Note: In a real application, you would check if user is admin
        // For now, we'll allow anyone to add publishers for testing

        var publisher = new Publisher
        {
            PName = request.PName,
            City = request.City
        };

        _context.Publishers.Add(publisher);
        await _context.SaveChangesAsync();

        return Ok(new
        {
            message = "Publisher added successfully",
            data = new
            {
                publisher.Id,
                publisher.PName,
                publisher.City
            }
        });
    }

    [HttpGet]
    public async Task<IActionResult> GetAllPublishers()
    {
        var publishers = await _context.Publishers.ToListAsync();
        return Ok(new
        {
            message = "Publishers retrieved successfully",
            data = publishers
        });
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> GetPublisherDetails(int id)
    {
        var publisher = await _context.Publishers
            .Include(p => p.Books)
            .ThenInclude(b => b.Author)
            .FirstOrDefaultAsync(p => p.Id == id);

        if (publisher == null)
        {
            return NotFound(new { message = "Publisher not found", data = (object?)null });
        }

        return Ok(new
        {
            message = "Publisher details retrieved successfully",
            data = publisher
        });
    }

    [HttpGet("search")]
    public async Task<IActionResult> SearchPublishers([FromQuery] string name)
    {
        if (string.IsNullOrEmpty(name))
        {
            return BadRequest(new { message = "Name parameter is required", data = (object?)null });
        }

        var publishers = await _context.Publishers
            .Where(p => p.PName.Contains(name))
            .ToListAsync();

        return Ok(new
        {
            message = "Publishers search completed successfully",
            data = publishers
        });
    }

    [HttpGet("{id}/books")]
    public async Task<IActionResult> GetPublisherBooks(int id)
    {
        var publisher = await _context.Publishers
            .Include(p => p.Books)
            .ThenInclude(b => b.Author)
            .FirstOrDefaultAsync(p => p.Id == id);

        if (publisher == null)
        {
            return NotFound(new { message = "Publisher not found", data = (object?)null });
        }

        return Ok(new
        {
            message = "Publisher books retrieved successfully",
            data = publisher.Books
        });
    }
}
