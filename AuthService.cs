using Microsoft.EntityFrameworkCore;
using System.Security.Cryptography;
using System.Text;

public class AuthService
{
    private readonly ELibraryContext _context;

    public AuthService(ELibraryContext context)
    {
        _context = context;
    }

    public async Task<User?> LoginAsync(LoginRequest request)
    {
        var user = await _context.Users
            .FirstOrDefaultAsync(u => u.Username == request.Username);

        if (user == null || !VerifyPassword(request.Password, user.Password))
        {
            return null;
        }

        return user;
    }

    public async Task<User> SignupAsync(SignupRequest request)
    {
        // Check if username already exists
        var existingUser = await _context.Users
            .FirstOrDefaultAsync(u => u.Username == request.Username);

        if (existingUser != null)
        {
            return null;
        }

        var user = new User
        {
            Username = request.Username,
            Password = HashPassword(request.Password),
            FName = request.FName,
            LName = request.LName,
            IsAdmin = false // New users are not admin by default
        };

        _context.Users.Add(user);
        await _context.SaveChangesAsync();

        return user;
    }

    private string HashPassword(string password)
    {
        using (var sha256 = SHA256.Create())
        {
            var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password));
            return Convert.ToBase64String(hashedBytes);
        }
    }

    private bool VerifyPassword(string password, string hashedPassword)
    {
        var hashedInput = HashPassword(password);
        return hashedInput == hashedPassword;
    }
}
