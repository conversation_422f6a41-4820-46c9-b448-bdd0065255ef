# ELibrary API

مكتبة إلكترونية API مبنية باستخدام ASP.NET Core مع Entity Framework Core و JWT Authentication.

## المتطلبات

- .NET 6.0 أو أحدث
- SQL Server
- Visual Studio أو VS Code

## إعداد المشروع

1. استنساخ المشروع
2. تحديث connection string في `appsettings.json`
3. تشغيل الأوامر التالية:

```bash
dotnet restore
dotnet ef migrations add InitialCreate
dotnet ef database update
dotnet run
```

## API Endpoints

### المصادقة (Authentication)

#### تسجيل الدخول
- **POST** `/api/auth/login`
- **Body**: 
```json
{
  "username": "string",
  "password": "string"
}
```
- **Response**: JWT Token

#### التسجيل
- **POST** `/api/auth/signup`
- **Body**:
```json
{
  "username": "string",
  "password": "string",
  "fname": "string",
  "lname": "string"
}
```

### الكتب (Books)

#### إضافة كتاب (Admin only)
- **POST** `/api/book`
- **Headers**: `Authorization: Bearer {token}`
- **Body**:
```json
{
  "title": "string",
  "type": "string",
  "price": 0,
  "pubId": 0,
  "authorId": 0
}
```

#### عرض جميع الكتب
- **GET** `/api/book`

#### تفاصيل كتاب
- **GET** `/api/book/{id}`

#### البحث في الكتب
- **GET** `/api/book/search?title={title}`

### المؤلفين (Authors)

#### إضافة مؤلف (Admin only)
- **POST** `/api/author`
- **Headers**: `Authorization: Bearer {token}`
- **Body**:
```json
{
  "fname": "string",
  "lname": "string",
  "country": "string",
  "city": "string",
  "address": "string"
}
```

#### عرض جميع المؤلفين
- **GET** `/api/author`

#### تفاصيل مؤلف
- **GET** `/api/author/{id}`

#### البحث في المؤلفين
- **GET** `/api/author/search?name={name}`

#### كتب المؤلف
- **GET** `/api/author/{id}/books`

### الناشرين (Publishers)

#### إضافة ناشر (Admin only)
- **POST** `/api/publisher`
- **Headers**: `Authorization: Bearer {token}`
- **Body**:
```json
{
  "pname": "string",
  "city": "string"
}
```

#### عرض جميع الناشرين
- **GET** `/api/publisher`

#### تفاصيل ناشر
- **GET** `/api/publisher/{id}`

#### البحث في الناشرين
- **GET** `/api/publisher/search?name={name}`

#### كتب الناشر
- **GET** `/api/publisher/{id}/books`

## ملاحظات

- المستخدمون الجدد ليسوا مديرين بشكل افتراضي
- فقط المديرون يمكنهم إضافة الكتب والمؤلفين والناشرين
- جميع المستخدمين يمكنهم البحث وعرض البيانات
