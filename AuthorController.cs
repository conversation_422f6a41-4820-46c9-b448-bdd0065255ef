using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

[ApiController]
[Route("api/[controller]")]
public class AuthorController : ControllerBase
{
    private readonly ELibraryContext _context;

    public AuthorController(ELibraryContext context)
    {
        _context = context;
    }

    [HttpPost]
    [Authorize]
    public async Task<IActionResult> AddAuthor([FromBody] Author author)
    {
        // Check if user is admin
        var isAdmin = User.FindFirst("IsAdmin")?.Value;
        if (isAdmin != "True")
        {
            return Forbid("Only admins can add authors");
        }

        _context.Authors.Add(author);
        await _context.SaveChangesAsync();

        return Ok(author);
    }

    [HttpGet]
    public async Task<IActionResult> GetAllAuthors()
    {
        var authors = await _context.Authors.ToListAsync();
        return Ok(authors);
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> GetAuthorDetails(int id)
    {
        var author = await _context.Authors
            .Include(a => a.Books)
            .ThenInclude(b => b.Publisher)
            .FirstOrDefaultAsync(a => a.Id == id);

        if (author == null)
        {
            return NotFound("Author not found");
        }

        return Ok(author);
    }

    [HttpGet("search")]
    public async Task<IActionResult> SearchAuthors([FromQuery] string name)
    {
        if (string.IsNullOrEmpty(name))
        {
            return BadRequest("Name parameter is required");
        }

        var authors = await _context.Authors
            .Where(a => a.FName.Contains(name) || a.LName.Contains(name))
            .ToListAsync();

        return Ok(authors);
    }

    [HttpGet("{id}/books")]
    public async Task<IActionResult> GetAuthorBooks(int id)
    {
        var author = await _context.Authors
            .Include(a => a.Books)
            .ThenInclude(b => b.Publisher)
            .FirstOrDefaultAsync(a => a.Id == id);

        if (author == null)
        {
            return NotFound("Author not found");
        }

        return Ok(author.Books);
    }
}
