using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

[ApiController]
[Route("api/[controller]")]
public class AuthorController : ControllerBase
{
    private readonly ELibraryContext _context;

    public AuthorController(ELibraryContext context)
    {
        _context = context;
    }

    [HttpPost]
    public async Task<IActionResult> AddAuthor([FromBody] AddAuthorRequest request)
    {
        // Note: In a real application, you would check if user is admin
        // For now, we'll allow anyone to add authors for testing

        var author = new Author
        {
            FName = request.FName,
            LName = request.LName,
            Country = request.Country,
            City = request.City,
            Address = request.Address
        };

        _context.Authors.Add(author);
        await _context.SaveChangesAsync();

        return Ok(new
        {
            message = "Author added successfully",
            data = new
            {
                author.Id,
                author.FName,
                author.LName,
                author.Country,
                author.City,
                author.Address
            }
        });
    }

    [HttpGet]
    public async Task<IActionResult> GetAllAuthors()
    {
        var authors = await _context.Authors.ToListAsync();
        return Ok(new
        {
            message = "Authors retrieved successfully",
            data = authors
        });
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> GetAuthorDetails(int id)
    {
        var author = await _context.Authors
            .Include(a => a.Books)
            .ThenInclude(b => b.Publisher)
            .FirstOrDefaultAsync(a => a.Id == id);

        if (author == null)
        {
            return NotFound(new { message = "Author not found", data = (object?)null });
        }

        return Ok(new
        {
            message = "Author details retrieved successfully",
            data = author
        });
    }

    [HttpGet("search")]
    public async Task<IActionResult> SearchAuthors([FromQuery] string name)
    {
        if (string.IsNullOrEmpty(name))
        {
            return BadRequest(new { message = "Name parameter is required", data = (object?)null });
        }

        var authors = await _context.Authors
            .Where(a => a.FName.Contains(name) || a.LName.Contains(name))
            .ToListAsync();

        return Ok(new
        {
            message = "Authors search completed successfully",
            data = authors
        });
    }

    [HttpGet("{id}/books")]
    public async Task<IActionResult> GetAuthorBooks(int id)
    {
        var author = await _context.Authors
            .Include(a => a.Books)
            .ThenInclude(b => b.Publisher)
            .FirstOrDefaultAsync(a => a.Id == id);

        if (author == null)
        {
            return NotFound(new { message = "Author not found", data = (object?)null });
        }

        return Ok(new
        {
            message = "Author books retrieved successfully",
            data = author.Books
        });
    }
}
