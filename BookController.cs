using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

[ApiController]
[Route("api/[controller]")]
public class BookController : ControllerBase
{
    private readonly ELibraryContext _context;

    public BookController(ELibraryContext context)
    {
        _context = context;
    }

    [HttpPost]
    [Authorize]
    public async Task<IActionResult> AddBook([FromBody] Book book)
    {
        // Check if user is admin
        var isAdmin = User.FindFirst("IsAdmin")?.Value;
        if (isAdmin != "True")
        {
            return Forbid("Only admins can add books");
        }

        // Validate that author and publisher exist
        var authorExists = await _context.Authors.AnyAsync(a => a.Id == book.AuthorId);
        var publisherExists = await _context.Publishers.AnyAsync(p => p.Id == book.PubId);

        if (!authorExists)
        {
            return BadRequest("Author not found");
        }

        if (!publisherExists)
        {
            return BadRequest("Publisher not found");
        }

        _context.Books.Add(book);
        await _context.SaveChangesAsync();

        return Ok(book);
    }

    [HttpGet]
    public async Task<IActionResult> GetAllBooks()
    {
        var books = await _context.Books
            .Include(b => b.Author)
            .Include(b => b.Publisher)
            .ToListAsync();

        return Ok(books);
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> GetBookDetails(int id)
    {
        var book = await _context.Books
            .Include(b => b.Author)
            .Include(b => b.Publisher)
            .FirstOrDefaultAsync(b => b.Id == id);

        if (book == null)
        {
            return NotFound("Book not found");
        }

        return Ok(book);
    }

    [HttpGet("search")]
    public async Task<IActionResult> SearchBooks([FromQuery] string title)
    {
        if (string.IsNullOrEmpty(title))
        {
            return BadRequest("Title parameter is required");
        }

        var books = await _context.Books
            .Include(b => b.Author)
            .Include(b => b.Publisher)
            .Where(b => b.Title.Contains(title))
            .ToListAsync();

        return Ok(books);
    }
}
