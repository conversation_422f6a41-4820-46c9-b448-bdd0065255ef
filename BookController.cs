using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

[ApiController]
[Route("api/[controller]")]
public class BookController : ControllerBase
{
    private readonly ELibraryContext _context;

    public BookController(ELibraryContext context)
    {
        _context = context;
    }

    [HttpPost]
    public async Task<IActionResult> AddBook([FromBody] AddBookRequest request)
    {
        // Note: In a real application, you would check if user is admin
        // For now, we'll allow anyone to add books for testing

        // Validate that author and publisher exist
        var author = await _context.Authors.FindAsync(request.AuthorId);
        var publisher = await _context.Publishers.FindAsync(request.PublisherId);

        if (author == null)
        {
            return BadRequest($"Author with ID {request.AuthorId} not found");
        }

        if (publisher == null)
        {
            return BadRequest($"Publisher with ID {request.PublisherId} not found");
        }

        // Create the book with the provided data
        var book = new Book
        {
            Title = request.Title,
            Type = request.Type,
            Price = request.Price,
            AuthorId = request.AuthorId,
            PubId = request.PublisherId
        };

        _context.Books.Add(book);
        await _context.SaveChangesAsync();

        // Return the book with author and publisher details
        var bookWithDetails = await _context.Books
            .Include(b => b.Author)
            .Include(b => b.Publisher)
            .FirstOrDefaultAsync(b => b.Id == book.Id);

        return Ok(new
        {
            message = "Book added successfully",
            data = bookWithDetails
        });
    }

    [HttpGet]
    public async Task<IActionResult> GetAllBooks()
    {
        var books = await _context.Books
            .Include(b => b.Author)
            .Include(b => b.Publisher)
            .ToListAsync();

        return Ok(new
        {
            message = "Books retrieved successfully",
            data = books
        });
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> GetBookDetails(int id)
    {
        var book = await _context.Books
            .Include(b => b.Author)
            .Include(b => b.Publisher)
            .FirstOrDefaultAsync(b => b.Id == id);

        if (book == null)
        {
            return NotFound(new { message = "Book not found", data = (object?)null });
        }

        return Ok(new
        {
            message = "Book details retrieved successfully",
            data = book
        });
    }

    [HttpGet("search")]
    public async Task<IActionResult> SearchBooks([FromQuery] string title)
    {
        if (string.IsNullOrEmpty(title))
        {
            return BadRequest(new { message = "Title parameter is required", data = (object?)null });
        }

        var books = await _context.Books
            .Include(b => b.Author)
            .Include(b => b.Publisher)
            .Where(b => b.Title.Contains(title))
            .ToListAsync();

        return Ok(new
        {
            message = "Books search completed successfully",
            data = books
        });
    }
}
